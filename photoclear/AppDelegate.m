//
//  AppDelegate.m
//  photoclear
//
//  Created by lifubing on 2021/6/15.
//

#import "AppDelegate.h"
#import "PhotoTools.h"
#import "photoclear-Swift.h"
#import "NotificationManager.h"
#import <BackgroundTasks/BackgroundTasks.h>

// Background task identifiers
static NSString * const kBackgroundPhotoRefreshTaskIdentifier = @"com.lfb.manager.photoclear.photo-refresh";
static NSString * const kBackgroundProcessingTaskIdentifier = @"com.lfb.manager.photoclear.photo-processing";

// Refresh intervals (in seconds)
static const NSTimeInterval kMinimumRefreshInterval = 60 * 5;  // 5分钟
static const NSTimeInterval kBackgroundTaskInterval = 60 * 15;  // 15分钟
static const NSTimeInterval kProcessingTaskInterval = 60 * 32; // 32分钟 与后台刷新15分钟一次错开

@interface AppDelegate ()

@property (nonatomic, assign) UIBackgroundTaskIdentifier backgroundTaskIdentifier;
@end

@implementation AppDelegate
@synthesize window = _window;

- (BOOL)application:(UIApplication *)application didFinishLaunchingWithOptions:(NSDictionary *)launchOptions {
    // 请求通知权限
    [[NotificationManager sharedManager] requestNotificationPermission:nil];
    
    // 注册后台任务
    [self registerBackgroundTasks];
    
    // 调度后台刷新任务
    [self scheduleBackgroundPhotoRefresh];
    
    return YES;
}

- (void)reloadWidgetTimelines {
    [WidgetKitHelper reloadAllWidgets];
}

#pragma mark - Background Tasks

- (void)registerBackgroundTasks {
    // 注册后台应用刷新任务 (更频繁的刷新)
//    BOOL refreshRegistered = [[BGTaskScheduler sharedScheduler] registerForTaskWithIdentifier:kBackgroundPhotoRefreshTaskIdentifier
//                                                                                   usingQueue:nil
//                                                                                launchHandler:^(__kindof BGTask * _Nonnull task) {
//        [self handleBackgroundPhotoRefresh:(BGAppRefreshTask *)task];
//    }];
//    
    // 注册后台处理任务 (更长时间的处理)
    [[BGTaskScheduler sharedScheduler] registerForTaskWithIdentifier:kBackgroundProcessingTaskIdentifier
                                                                                      usingQueue:nil
                                                                                   launchHandler:^(__kindof BGTask * _Nonnull task) {
        [self handleBackgroundPhotoProcessing:(BGProcessingTask *)task];
    }];
}

- (void)scheduleBackgroundPhotoRefresh {
//    [self scheduleBackgroundRefreshTask];
    [self scheduleBackgroundProcessingTask];
}

- (void)scheduleBackgroundRefreshTask {
    // 取消之前的后台刷新任务
    [[BGTaskScheduler sharedScheduler] cancelTaskRequestWithIdentifier:kBackgroundPhotoRefreshTaskIdentifier];
    
    // 创建新的后台应用刷新请求 (15分钟间隔)
    BGAppRefreshTaskRequest *refreshRequest = [[BGAppRefreshTaskRequest alloc] initWithIdentifier:kBackgroundPhotoRefreshTaskIdentifier];
    refreshRequest.earliestBeginDate = [NSDate dateWithTimeIntervalSinceNow:kBackgroundTaskInterval]; // 15分钟
    
    NSError *refreshError = nil;
    BOOL refreshSuccess = [[BGTaskScheduler sharedScheduler] submitTaskRequest:refreshRequest error:&refreshError];
    
    if (!refreshSuccess) {
        NSLog(@"Failed to schedule background refresh task: %@", refreshError.localizedDescription);
    } else {
        NSLog(@"Successfully scheduled background refresh task for %.0f minutes", kBackgroundTaskInterval / 60.0);
    }
}

- (void)scheduleBackgroundProcessingTask {
    // 取消之前的后台处理任务
    [[BGTaskScheduler sharedScheduler] cancelTaskRequestWithIdentifier:kBackgroundProcessingTaskIdentifier];
    
    // 创建新的后台处理请求 (30分钟间隔)
    BGProcessingTaskRequest *processingRequest = [[BGProcessingTaskRequest alloc] initWithIdentifier:kBackgroundProcessingTaskIdentifier];
    processingRequest.earliestBeginDate = [NSDate dateWithTimeIntervalSinceNow:kProcessingTaskInterval]; // 30分钟
    processingRequest.requiresNetworkConnectivity = NO;
    processingRequest.requiresExternalPower = NO;
    
    NSError *processingError = nil;
    BOOL processingSuccess = [[BGTaskScheduler sharedScheduler] submitTaskRequest:processingRequest error:&processingError];
    
    if (!processingSuccess) {
        NSLog(@"Failed to schedule background processing task: %@", processingError.localizedDescription);
    } else {
        NSLog(@"Successfully scheduled background processing task for %.0f minutes", kProcessingTaskInterval / 60.0);
    }
}

- (void)handleBackgroundPhotoRefresh:(BGAppRefreshTask *)task {
    NSLog(@"Background photo refresh task started");
        
    // 设置任务过期处理
    __weak typeof(task) weakTask = task; // 使用弱引用
    task.expirationHandler = ^{
        NSLog(@"Background photo refresh task expired");
        [self scheduleBackgroundRefreshTask]; // 重新调度
        [weakTask setTaskCompletedWithSuccess:NO];
    };
    
    // 执行快速照片缓存更新 (使用较短的间隔)
    [self performBackgroundPhotoUpdateWithCompletion:^(BOOL success) {
        NSLog(@"Background photo refresh completed with success: %@", success ? @"YES" : @"NO");
        
        // 发送完成通知
        NSDateFormatter *formatter = [[NSDateFormatter alloc] init];
        [formatter setDateFormat:@"HH:mm:ss"];
        NSString *currentTime = [formatter stringFromDate:[NSDate date]];
        
        NSString *baseMessage = success ? @"后台照片刷新完成" : @"后台照片刷新失败";
        NSString *resultMessage = [NSString stringWithFormat:@"%@ - %@", baseMessage, currentTime];
        
        [[NotificationManager sharedManager] sendNotificationWithTitle:@"PhotoClear" body:resultMessage];
        
        // 调度下一次后台刷新任务
        [self scheduleBackgroundRefreshTask];
        
        [task setTaskCompletedWithSuccess:success];
    }];
}

- (void)handleBackgroundPhotoProcessing:(BGProcessingTask *)task {
    NSLog(@"Background photo processing task started");
    
    // 设置任务过期处理
    __weak typeof(task) weakTask = task; // 使用弱引用
    task.expirationHandler = ^{
        NSLog(@"Background photo processing task expired");
        [self scheduleBackgroundProcessingTask]; // 重新调度
        [weakTask setTaskCompletedWithSuccess:NO];
    };
    
    // 执行更全面的照片处理 (使用更短的间隔强制更新)
    [self performBackgroundPhotoUpdateWithCompletion:^(BOOL success) {
        NSLog(@"Background photo processing completed with success: %@", success ? @"YES" : @"NO");
        
        // 发送完成通知
        NSDateFormatter *formatter = [[NSDateFormatter alloc] init];
        [formatter setDateFormat:@"HH:mm:ss"];
        NSString *currentTime = [formatter stringFromDate:[NSDate date]];
        
        NSString *baseMessage = success ? @"后台照片处理完成" : @"后台照片处理失败";
        NSString *resultMessage = [NSString stringWithFormat:@"%@ - %@", baseMessage, currentTime];
        
        [[NotificationManager sharedManager] sendNotificationWithTitle:@"PhotoClear" body:resultMessage];
        
        // 调度下一次后台处理任务
        [self scheduleBackgroundProcessingTask];
        
        [task setTaskCompletedWithSuccess:success];
    }];
}

- (void)performBackgroundPhotoUpdateWithCompletion:(void(^)(BOOL success))completion {
    // 开始后台任务以防止应用被挂起
    self.backgroundTaskIdentifier = [[UIApplication sharedApplication] beginBackgroundTaskWithName:@"PhotoCacheUpdate" expirationHandler:^{
        // 任务即将过期时的处理
        NSLog(@"Background photo update task will expire");
        if (self.backgroundTaskIdentifier != UIBackgroundTaskInvalid) {
            [[UIApplication sharedApplication] endBackgroundTask:self.backgroundTaskIdentifier];
            self.backgroundTaskIdentifier = UIBackgroundTaskInvalid;
        }
        if (completion) {
            completion(NO);
        }
    }];
    
    // 同步Widget的删除和操作记录
    [PhotoTools syncDeleteArrayFromWidget];
    [PhotoTools syncOperatedPhotosFromWidget];
    
    
    // 检查上次缓存时间，根据指定间隔决定是否重新缓存
    NSUserDefaults *defaults = [[NSUserDefaults alloc] initWithSuiteName:@"group.com.lfb.manager.photoclear.shared"];
    NSDate *lastCacheTime = [defaults objectForKey:@"lastPhotoCacheTime"];

    BOOL shouldCache = NO;
    NSTimeInterval minInterval = 60 * 30; // 30分钟

    if (!lastCacheTime) {
        shouldCache = YES;
    } else {
        NSTimeInterval timeSinceLastCache = [[NSDate date] timeIntervalSinceDate:lastCacheTime];
        if (timeSinceLastCache > minInterval) {
            shouldCache = YES;
        }
    }
    
    if (!shouldCache) {
        completion(YES);
        return;
    }
    
    [PhotoTools normalCacheSmallRandomPhotosForWidgetCompletion:^(BOOL success) {
        if (success) {
            NSUserDefaults *defaults = [[NSUserDefaults alloc] initWithSuiteName:@"group.com.lfb.manager.photoclear.shared"];
            [defaults setObject:[NSDate date] forKey:@"lastPhotoCacheTime"];
            [defaults synchronize];

            // 通知Widget更新
            [self reloadWidgetTimelines];
            
            NSLog(@"Background photo cache updated successfully");
        } else {
            NSLog(@"Background photo cache update failed");
        }
        
        // 结束后台任务
        if (self.backgroundTaskIdentifier != UIBackgroundTaskInvalid) {
            [[UIApplication sharedApplication] endBackgroundTask:self.backgroundTaskIdentifier];
            self.backgroundTaskIdentifier = UIBackgroundTaskInvalid;
        }
        
        if (completion) {
            completion(success);
        }
    }];
}

// 兼容性方法，保持原有接口
- (void)performBackgroundPhotoUpdate:(void(^)(BOOL success))completion {
    [self performBackgroundPhotoUpdateWithCompletion:completion];
}

#pragma mark - UISceneSession lifecycle


- (UISceneConfiguration *)application:(UIApplication *)application configurationForConnectingSceneSession:(UISceneSession *)connectingSceneSession options:(UISceneConnectionOptions *)options {
    // Called when a new scene session is being created.
    // Use this method to select a configuration to create the new scene with.
    return [[UISceneConfiguration alloc] initWithName:@"Default Configuration" sessionRole:connectingSceneSession.role];
}


- (void)application:(UIApplication *)application didDiscardSceneSessions:(NSSet<UISceneSession *> *)sceneSessions {
    // Called when the user discards a scene session.
    // If any sessions were discarded while the application was not running, this will be called shortly after application:didFinishLaunchingWithOptions.
    // Use this method to release any resources that were specific to the discarded scenes, as they will not return.
}


@end
